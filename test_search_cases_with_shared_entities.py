#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的 search_cases_with_shared_entities 函数
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import List, Dict, Optional, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.database import get_db
from app.services.scene_qz.scene_qz_service import SceneQzService
from app.log.log_utils import LogUtils


async def test_search_cases_with_shared_entities():
    """测试优化后的 search_cases_with_shared_entities 函数"""
    
    print("=" * 80)
    print("开始测试优化后的 search_cases_with_shared_entities 函数")
    print("=" * 80)
    
    # 获取数据库连接
    db = next(get_db())
    
    try:
        # 测试用例1：基本功能测试 - 两个实体，有身份证
        print("\n测试用例1：基本功能测试 - 两个实体，有身份证")
        print("-" * 50)
        
        entities_1 = [
            {"name": "张三", "id_card": "110101199001011234"},
            {"name": "李四", "id_card": "110101199002022345"}
        ]
        case_type_1 = "盗窃"
        
        start_time = datetime.now()
        result_1 = await SceneQzService.search_cases_with_shared_entities(
            entities=entities_1,
            case_type=case_type_1,
            page=1,
            page_size=10,
            db=db
        )
        end_time = datetime.now()
        execution_time_1 = (end_time - start_time).total_seconds()
        
        print(f"执行时间: {execution_time_1:.3f} 秒")
        print(f"返回结果: {result_1}")
        print(f"找到案件数量: {result_1.get('total', 0)}")
        
        # 测试用例2：只有姓名，无身份证
        print("\n测试用例2：只有姓名，无身份证")
        print("-" * 50)
        
        entities_2 = [
            {"name": "王五"},
            {"name": "赵六"}
        ]
        case_type_2 = "诈骗"
        
        start_time = datetime.now()
        result_2 = await SceneQzService.search_cases_with_shared_entities(
            entities=entities_2,
            case_type=case_type_2,
            page=1,
            page_size=10,
            db=db
        )
        end_time = datetime.now()
        execution_time_2 = (end_time - start_time).total_seconds()
        
        print(f"执行时间: {execution_time_2:.3f} 秒")
        print(f"返回结果: {result_2}")
        print(f"找到案件数量: {result_2.get('total', 0)}")
        
        # 测试用例3：混合情况 - 部分有身份证，部分没有
        print("\n测试用例3：混合情况 - 部分有身份证，部分没有")
        print("-" * 50)
        
        entities_3 = [
            {"name": "陈七", "id_card": "110101199003033456"},
            {"name": "周八"}
        ]
        case_type_3 = "抢劫"
        
        start_time = datetime.now()
        result_3 = await SceneQzService.search_cases_with_shared_entities(
            entities=entities_3,
            case_type=case_type_3,
            page=1,
            page_size=10,
            db=db
        )
        end_time = datetime.now()
        execution_time_3 = (end_time - start_time).total_seconds()
        
        print(f"执行时间: {execution_time_3:.3f} 秒")
        print(f"返回结果: {result_3}")
        print(f"找到案件数量: {result_3.get('total', 0)}")
        
        # 测试用例4：大量实体测试（性能测试）
        print("\n测试用例4：大量实体测试（性能测试）")
        print("-" * 50)
        
        entities_4 = [
            {"name": f"测试人员{i}", "id_card": f"11010119900101{i:04d}"} 
            for i in range(1, 6)  # 5个实体
        ]
        case_type_4 = "盗窃"
        
        start_time = datetime.now()
        result_4 = await SceneQzService.search_cases_with_shared_entities(
            entities=entities_4,
            case_type=case_type_4,
            page=1,
            page_size=20,
            db=db
        )
        end_time = datetime.now()
        execution_time_4 = (end_time - start_time).total_seconds()
        
        print(f"执行时间: {execution_time_4:.3f} 秒")
        print(f"返回结果: {result_4}")
        print(f"找到案件数量: {result_4.get('total', 0)}")
        
        # 测试用例5：分页测试
        print("\n测试用例5：分页测试")
        print("-" * 50)
        
        entities_5 = [
            {"name": "张三"},
            {"name": "李四"}
        ]
        case_type_5 = "盗窃"
        
        # 测试第1页
        start_time = datetime.now()
        result_5_page1 = await SceneQzService.search_cases_with_shared_entities(
            entities=entities_5,
            case_type=case_type_5,
            page=1,
            page_size=5,
            db=db
        )
        end_time = datetime.now()
        execution_time_5_page1 = (end_time - start_time).total_seconds()
        
        print(f"第1页执行时间: {execution_time_5_page1:.3f} 秒")
        print(f"第1页结果: {result_5_page1}")
        
        # 测试第2页
        start_time = datetime.now()
        result_5_page2 = await SceneQzService.search_cases_with_shared_entities(
            entities=entities_5,
            case_type=case_type_5,
            page=2,
            page_size=5,
            db=db
        )
        end_time = datetime.now()
        execution_time_5_page2 = (end_time - start_time).total_seconds()
        
        print(f"第2页执行时间: {execution_time_5_page2:.3f} 秒")
        print(f"第2页结果: {result_5_page2}")
        
        # 测试用例6：边界情况测试 - 空实体列表
        print("\n测试用例6：边界情况测试 - 空实体列表")
        print("-" * 50)
        
        entities_6 = []
        case_type_6 = "盗窃"
        
        start_time = datetime.now()
        result_6 = await SceneQzService.search_cases_with_shared_entities(
            entities=entities_6,
            case_type=case_type_6,
            page=1,
            page_size=10,
            db=db
        )
        end_time = datetime.now()
        execution_time_6 = (end_time - start_time).total_seconds()
        
        print(f"执行时间: {execution_time_6:.3f} 秒")
        print(f"返回结果: {result_6}")
        print(f"找到案件数量: {result_6.get('total', 0)}")
        
        # 测试用例7：边界情况测试 - 无效实体名称
        print("\n测试用例7：边界情况测试 - 无效实体名称")
        print("-" * 50)
        
        entities_7 = [
            {"name": ""},  # 空名称
            {"name": "不存在的人员"}
        ]
        case_type_7 = "盗窃"
        
        start_time = datetime.now()
        result_7 = await SceneQzService.search_cases_with_shared_entities(
            entities=entities_7,
            case_type=case_type_7,
            page=1,
            page_size=10,
            db=db
        )
        end_time = datetime.now()
        execution_time_7 = (end_time - start_time).total_seconds()
        
        print(f"执行时间: {execution_time_7:.3f} 秒")
        print(f"返回结果: {result_7}")
        print(f"找到案件数量: {result_7.get('total', 0)}")
        
        # 性能总结
        print("\n" + "=" * 80)
        print("性能测试总结")
        print("=" * 80)
        print(f"测试用例1（两个实体，有身份证）: {execution_time_1:.3f} 秒")
        print(f"测试用例2（只有姓名，无身份证）: {execution_time_2:.3f} 秒")
        print(f"测试用例3（混合情况）: {execution_time_3:.3f} 秒")
        print(f"测试用例4（5个实体性能测试）: {execution_time_4:.3f} 秒")
        print(f"测试用例5（分页测试第1页）: {execution_time_5_page1:.3f} 秒")
        print(f"测试用例5（分页测试第2页）: {execution_time_5_page2:.3f} 秒")
        print(f"测试用例6（空实体列表）: {execution_time_6:.3f} 秒")
        print(f"测试用例7（无效实体名称）: {execution_time_7:.3f} 秒")
        
        avg_time = (execution_time_1 + execution_time_2 + execution_time_3 + 
                   execution_time_4 + execution_time_5_page1 + execution_time_5_page2 + 
                   execution_time_6 + execution_time_7) / 8
        print(f"平均执行时间: {avg_time:.3f} 秒")
        
        print("\n测试完成！")
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_search_cases_with_shared_entities())
