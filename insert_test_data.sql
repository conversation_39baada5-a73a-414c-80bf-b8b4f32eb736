-- 补充测试数据的SQL脚本
-- 包含多样化的身份证情况和共享实体案件

-- 插入案件和实体数据
INSERT INTO qz_case_feature (case_id, entity_id, feature_type, feature_value) VALUES

-- 案件1: 盗窃案 - 王五和李四都有身份证（共享实体：王五）
('TEST001', 0, 'CASE_TYPE', '盗窃'),
('TEST001', 0, 'LOCATION', '116.408000 39.906000'),
('TEST001', 1, 'ID_CARD', '******************'),
('TEST001', 1, 'ENTITY_NAME', '王五'),
('TEST001', 1, 'ENTITY_TYPE', '嫌疑人'),
('TEST001', 2, 'ID_CARD', '******************'),
('TEST001', 2, 'ENTITY_NAME', '李四'),
('TEST001', 2, 'ENTITY_TYPE', '同伙'),

-- 案件2: 诈骗案 - 王五和张三都有身份证（共享实体：王五）
('TEST002', 0, 'CASE_TYPE', '诈骗'),
('TEST002', 0, 'LOCATION', '116.409000 39.907000'),
('TEST002', 1, 'ID_CARD', '******************'),
('TEST002', 1, 'ENTITY_NAME', '王五'),
('TEST002', 1, 'ENTITY_TYPE', '嫌疑人'),
('TEST002', 2, 'ID_CARD', '******************'),
('TEST002', 2, 'ENTITY_NAME', '张三'),
('TEST002', 2, 'ENTITY_TYPE', '受害者'),

-- 案件3: 抢劫案 - 李四和赵六都有身份证（共享实体：李四）
('TEST003', 0, 'CASE_TYPE', '抢劫'),
('TEST003', 0, 'LOCATION', '116.410000 39.908000'),
('TEST003', 1, 'ID_CARD', '******************'),
('TEST003', 1, 'ENTITY_NAME', '李四'),
('TEST003', 1, 'ENTITY_TYPE', '嫌疑人'),
('TEST003', 2, 'ID_CARD', '310101199504043456'),
('TEST003', 2, 'ENTITY_NAME', '赵六'),
('TEST003', 2, 'ENTITY_TYPE', '受害者'),

-- 案件4: 盗窃案 - 只有姓名，没有身份证
('TEST004', 0, 'CASE_TYPE', '盗窃'),
('TEST004', 0, 'LOCATION', '116.411000 39.909000'),
('TEST004', 1, 'ENTITY_NAME', '陈七'),
('TEST004', 1, 'ENTITY_TYPE', '嫌疑人'),
('TEST004', 2, 'ENTITY_NAME', '孙八'),
('TEST004', 2, 'ENTITY_TYPE', '同伙'),

-- 案件5: 诈骗案 - 只有姓名，没有身份证（共享实体：陈七）
('TEST005', 0, 'CASE_TYPE', '诈骗'),
('TEST005', 0, 'LOCATION', '116.412000 39.910000'),
('TEST005', 1, 'ENTITY_NAME', '陈七'),
('TEST005', 1, 'ENTITY_TYPE', '嫌疑人'),
('TEST005', 2, 'ENTITY_NAME', '吴九'),
('TEST005', 2, 'ENTITY_TYPE', '受害者'),

-- 案件6: 抢劫案 - 混合情况：部分有身份证，部分没有
('TEST006', 0, 'CASE_TYPE', '抢劫'),
('TEST006', 0, 'LOCATION', '116.413000 39.911000'),
('TEST006', 1, 'ID_CARD', '******************'),
('TEST006', 1, 'ENTITY_NAME', '王五'),
('TEST006', 1, 'ENTITY_TYPE', '嫌疑人'),
('TEST006', 2, 'ENTITY_NAME', '陈七'),
('TEST006', 2, 'ENTITY_TYPE', '同伙'),
('TEST006', 3, 'ENTITY_NAME', '郑十'),
('TEST006', 3, 'ENTITY_TYPE', '受害者'),

-- 案件7: 盗窃案 - 混合情况：部分有身份证，部分没有（共享实体：张三）
('TEST007', 0, 'CASE_TYPE', '盗窃'),
('TEST007', 0, 'LOCATION', '116.414000 39.912000'),
('TEST007', 1, 'ID_CARD', '******************'),
('TEST007', 1, 'ENTITY_NAME', '张三'),
('TEST007', 1, 'ENTITY_TYPE', '嫌疑人'),
('TEST007', 2, 'ENTITY_NAME', '孙八'),
('TEST007', 2, 'ENTITY_TYPE', '同伙'),

-- 案件8: 诈骗案 - 三个人都有身份证
('TEST008', 0, 'CASE_TYPE', '诈骗'),
('TEST008', 0, 'LOCATION', '116.415000 39.913000'),
('TEST008', 1, 'ID_CARD', '******************'),
('TEST008', 1, 'ENTITY_NAME', '张三'),
('TEST008', 1, 'ENTITY_TYPE', '嫌疑人'),
('TEST008', 2, 'ID_CARD', '******************'),
('TEST008', 2, 'ENTITY_NAME', '李四'),
('TEST008', 2, 'ENTITY_TYPE', '同伙'),
('TEST008', 3, 'ID_CARD', '310101199504043456'),
('TEST008', 3, 'ENTITY_NAME', '赵六'),
('TEST008', 3, 'ENTITY_TYPE', '受害者'),

-- 案件9: 抢劫案 - 三个人都没有身份证
('TEST009', 0, 'CASE_TYPE', '抢劫'),
('TEST009', 0, 'LOCATION', '116.416000 39.914000'),
('TEST009', 1, 'ENTITY_NAME', '陈七'),
('TEST009', 1, 'ENTITY_TYPE', '嫌疑人'),
('TEST009', 2, 'ENTITY_NAME', '孙八'),
('TEST009', 2, 'ENTITY_TYPE', '同伙'),
('TEST009', 3, 'ENTITY_NAME', '吴九'),
('TEST009', 3, 'ENTITY_TYPE', '受害者'),

-- 案件10: 盗窃案 - 复杂混合情况
('TEST010', 0, 'CASE_TYPE', '盗窃'),
('TEST010', 0, 'LOCATION', '116.417000 39.915000'),
('TEST010', 1, 'ID_CARD', '******************'),
('TEST010', 1, 'ENTITY_NAME', '王五'),
('TEST010', 1, 'ENTITY_TYPE', '嫌疑人'),
('TEST010', 2, 'ENTITY_NAME', '陈七'),
('TEST010', 2, 'ENTITY_TYPE', '同伙'),
('TEST010', 3, 'ID_CARD', '******************'),
('TEST010', 3, 'ENTITY_NAME', '李四'),
('TEST010', 3, 'ENTITY_TYPE', '受害者'),
('TEST010', 4, 'ENTITY_NAME', '郑十'),
('TEST010', 4, 'ENTITY_TYPE', '目击者'),

-- 案件11: 诈骗案 - 同名不同人的情况（测试身份证区分）
('TEST011', 0, 'CASE_TYPE', '诈骗'),
('TEST011', 0, 'LOCATION', '116.418000 39.916000'),
('TEST011', 1, 'ID_CARD', '******************'),
('TEST011', 1, 'ENTITY_NAME', '张三'),
('TEST011', 1, 'ENTITY_TYPE', '嫌疑人'),
('TEST011', 2, 'ID_CARD', '******************'),
('TEST011', 2, 'ENTITY_NAME', '张三'),
('TEST011', 2, 'ENTITY_TYPE', '受害者'),

-- 案件12: 抢劫案 - 同名不同人，一个有身份证一个没有
('TEST012', 0, 'CASE_TYPE', '抢劫'),
('TEST012', 0, 'LOCATION', '116.419000 39.917000'),
('TEST012', 1, 'ID_CARD', '******************'),
('TEST012', 1, 'ENTITY_NAME', '张三'),
('TEST012', 1, 'ENTITY_TYPE', '嫌疑人'),
('TEST012', 2, 'ENTITY_NAME', '张三'),
('TEST012', 2, 'ENTITY_TYPE', '目击者'),

-- 案件13: 盗窃案 - 大量实体测试
('TEST013', 0, 'CASE_TYPE', '盗窃'),
('TEST013', 0, 'LOCATION', '116.420000 39.918000'),
('TEST013', 1, 'ID_CARD', '******************'),
('TEST013', 1, 'ENTITY_NAME', '王五'),
('TEST013', 1, 'ENTITY_TYPE', '嫌疑人'),
('TEST013', 2, 'ID_CARD', '******************'),
('TEST013', 2, 'ENTITY_NAME', '李四'),
('TEST013', 2, 'ENTITY_TYPE', '同伙'),
('TEST013', 3, 'ENTITY_NAME', '陈七'),
('TEST013', 3, 'ENTITY_TYPE', '同伙'),
('TEST013', 4, 'ID_CARD', '310101199504043456'),
('TEST013', 4, 'ENTITY_NAME', '赵六'),
('TEST013', 4, 'ENTITY_TYPE', '受害者'),
('TEST013', 5, 'ENTITY_NAME', '孙八'),
('TEST013', 5, 'ENTITY_TYPE', '目击者'),

-- 案件14: 诈骗案 - 测试边界情况
('TEST014', 0, 'CASE_TYPE', '诈骗'),
('TEST014', 0, 'LOCATION', '116.421000 39.919000'),
('TEST014', 1, 'ENTITY_NAME', '单独一人'),
('TEST014', 1, 'ENTITY_TYPE', '嫌疑人'),

-- 案件15: 抢劫案 - 另一个单人案件
('TEST015', 0, 'CASE_TYPE', '抢劫'),
('TEST015', 0, 'LOCATION', '116.422000 39.920000'),
('TEST015', 1, 'ID_CARD', '******************'),
('TEST015', 1, 'ENTITY_NAME', '独行侠'),
('TEST015', 1, 'ENTITY_TYPE', '嫌疑人');

-- 插入对应的police_records数据
INSERT INTO police_records (police_number, police_time, source) VALUES
('TEST001', '2024-01-15 10:30:00', 0),
('TEST002', '2024-01-16 14:20:00', 0),
('TEST003', '2024-01-17 09:15:00', 0),
('TEST004', '2024-01-18 16:45:00', 0),
('TEST005', '2024-01-19 11:30:00', 0),
('TEST006', '2024-01-20 13:20:00', 0),
('TEST007', '2024-01-21 15:10:00', 0),
('TEST008', '2024-01-22 08:45:00', 0),
('TEST009', '2024-01-23 17:30:00', 0),
('TEST010', '2024-01-24 12:15:00', 0),
('TEST011', '2024-01-25 14:50:00', 0),
('TEST012', '2024-01-26 10:20:00', 0),
('TEST013', '2024-01-27 16:30:00', 0),
('TEST014', '2024-01-28 09:40:00', 0),
('TEST015', '2024-01-29 11:55:00', 0);
