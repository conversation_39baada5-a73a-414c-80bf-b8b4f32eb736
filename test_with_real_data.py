#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实数据测试优化后的 search_cases_with_shared_entities 函数
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import List, Dict, Optional, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.database import get_db
from app.services.scene_qz.scene_qz_service import SceneQzService
from app.log.log_utils import LogUtils
from sqlalchemy import text


async def get_sample_data():
    """获取数据库中的样本数据"""
    db = next(get_db())
    
    try:
        # 查询一些实际存在的实体名称和案件类型
        sample_query = """
        SELECT DISTINCT 
            qcf1.feature_value as entity_name,
            qcf2.feature_value as case_type,
            qcf3.feature_value as id_card
        FROM qz_case_feature qcf1
        JOIN qz_case_feature qcf2 ON qcf1.case_id = qcf2.case_id
        LEFT JOIN qz_case_feature qcf3 ON qcf1.case_id = qcf3.case_id AND qcf1.entity_id = qcf3.entity_id AND qcf3.feature_type = 'ID_CARD'
        WHERE qcf1.feature_type = 'ENTITY_NAME' 
          AND qcf2.feature_type = 'CASE_TYPE'
          AND qcf2.entity_id = 0
          AND qcf1.entity_id > 0
          AND qcf1.feature_value IS NOT NULL
          AND qcf2.feature_value IS NOT NULL
        LIMIT 20
        """
        
        result = db.execute(text(sample_query))
        sample_data = result.fetchall()
        
        print("数据库中的样本数据:")
        for i, row in enumerate(sample_data):
            entity_name = row[0]
            case_type = row[1]
            id_card = row[2] if row[2] else "无"
            print(f"  {i+1}. 实体: {entity_name}, 案件类型: {case_type}, 身份证: {id_card}")
        
        return sample_data
        
    finally:
        db.close()


async def test_with_real_data():
    """使用真实数据测试优化后的函数"""
    
    print("=" * 80)
    print("使用真实数据测试优化后的 search_cases_with_shared_entities 函数")
    print("=" * 80)
    
    # 获取样本数据
    sample_data = await get_sample_data()
    
    if not sample_data:
        print("数据库中没有找到样本数据，无法进行测试")
        return
    
    # 获取数据库连接
    db = next(get_db())
    
    try:
        # 从样本数据中选择一些实体进行测试
        if len(sample_data) >= 2:
            # 测试用例1：使用前两个实体
            print("\n测试用例1：使用真实数据中的前两个实体")
            print("-" * 50)
            
            entity1 = sample_data[0]
            entity2 = sample_data[1]
            
            entities_1 = [
                {"name": entity1[0], "id_card": entity1[2] if entity1[2] else None},
                {"name": entity2[0], "id_card": entity2[2] if entity2[2] else None}
            ]
            case_type_1 = entity1[1]  # 使用第一个实体的案件类型
            
            print(f"查询实体: {entities_1}")
            print(f"案件类型: {case_type_1}")
            
            start_time = datetime.now()
            result_1 = await SceneQzService.search_cases_with_shared_entities(
                entities=entities_1,
                case_type=case_type_1,
                page=1,
                page_size=10,
                db=db
            )
            end_time = datetime.now()
            execution_time_1 = (end_time - start_time).total_seconds()
            
            print(f"执行时间: {execution_time_1:.3f} 秒")
            print(f"找到案件数量: {result_1.get('total', 0)}")
            
            if result_1.get('items'):
                print("找到的案件:")
                for i, item in enumerate(result_1['items'][:3]):  # 只显示前3个
                    print(f"  案件{i+1}: {item['case']}")
                    print(f"    案件类型: {item['case_type']}")
                    print(f"    涉及角色: {[r['name'] for r in item['roles']]}")
                    print(f"    其他涉及人员: {[p['name'] for p in item['other_involved']]}")
            
        # 测试用例2：使用相同案件类型的多个实体
        print("\n测试用例2：使用相同案件类型的多个实体")
        print("-" * 50)
        
        # 找到相同案件类型的实体
        same_case_type_entities = []
        target_case_type = None
        
        for data in sample_data:
            if target_case_type is None:
                target_case_type = data[1]
                same_case_type_entities.append(data)
            elif data[1] == target_case_type and len(same_case_type_entities) < 3:
                same_case_type_entities.append(data)
        
        if len(same_case_type_entities) >= 2:
            entities_2 = [
                {"name": entity[0], "id_card": entity[2] if entity[2] else None}
                for entity in same_case_type_entities[:2]
            ]
            
            print(f"查询实体: {entities_2}")
            print(f"案件类型: {target_case_type}")
            
            start_time = datetime.now()
            result_2 = await SceneQzService.search_cases_with_shared_entities(
                entities=entities_2,
                case_type=target_case_type,
                page=1,
                page_size=10,
                db=db
            )
            end_time = datetime.now()
            execution_time_2 = (end_time - start_time).total_seconds()
            
            print(f"执行时间: {execution_time_2:.3f} 秒")
            print(f"找到案件数量: {result_2.get('total', 0)}")
            
            if result_2.get('items'):
                print("找到的案件:")
                for i, item in enumerate(result_2['items'][:3]):  # 只显示前3个
                    print(f"  案件{i+1}: {item['case']}")
                    print(f"    案件类型: {item['case_type']}")
                    print(f"    涉及角色: {[r['name'] for r in item['roles']]}")
        
        # 测试用例3：性能压力测试 - 使用更多实体
        print("\n测试用例3：性能压力测试 - 使用更多实体")
        print("-" * 50)
        
        if len(sample_data) >= 5:
            entities_3 = [
                {"name": entity[0], "id_card": entity[2] if entity[2] else None}
                for entity in sample_data[:5]
            ]
            case_type_3 = sample_data[0][1]
            
            print(f"查询{len(entities_3)}个实体")
            print(f"案件类型: {case_type_3}")
            
            start_time = datetime.now()
            result_3 = await SceneQzService.search_cases_with_shared_entities(
                entities=entities_3,
                case_type=case_type_3,
                page=1,
                page_size=20,
                db=db
            )
            end_time = datetime.now()
            execution_time_3 = (end_time - start_time).total_seconds()
            
            print(f"执行时间: {execution_time_3:.3f} 秒")
            print(f"找到案件数量: {result_3.get('total', 0)}")
        
        # 测试用例4：分页性能测试
        print("\n测试用例4：分页性能测试")
        print("-" * 50)
        
        if len(sample_data) >= 2:
            entities_4 = [
                {"name": sample_data[0][0]},
                {"name": sample_data[1][0]}
            ]
            case_type_4 = sample_data[0][1]
            
            # 测试多个页面
            total_time = 0
            for page in range(1, 4):  # 测试前3页
                start_time = datetime.now()
                result_page = await SceneQzService.search_cases_with_shared_entities(
                    entities=entities_4,
                    case_type=case_type_4,
                    page=page,
                    page_size=5,
                    db=db
                )
                end_time = datetime.now()
                page_time = (end_time - start_time).total_seconds()
                total_time += page_time
                
                print(f"第{page}页执行时间: {page_time:.3f} 秒, 结果数量: {len(result_page.get('items', []))}")
            
            print(f"分页测试总时间: {total_time:.3f} 秒")
            print(f"平均每页时间: {total_time/3:.3f} 秒")
        
        print("\n" + "=" * 80)
        print("真实数据测试完成！")
        print("=" * 80)
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_with_real_data())
