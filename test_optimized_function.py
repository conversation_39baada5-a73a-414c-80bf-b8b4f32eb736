#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用新测试数据验证优化后的 search_cases_with_shared_entities 函数
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import List, Dict, Optional, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.database import get_db
from app.services.scene_qz.scene_qz_service import SceneQzService
from app.log.log_utils import LogUtils


async def test_optimized_function():
    """测试优化后的函数"""

    print("=" * 80)
    print("使用新测试数据验证优化后的 search_cases_with_shared_entities 函数")
    print("=" * 80)

    # 获取数据库连接
    db = next(get_db())

    try:
        # 测试用例1：王五和李四（都有身份证的共享实体）
        print("\n测试用例1：王五和李四（都有身份证的共享实体）")
        print("-" * 60)

        entities_1 = [
            {"name": "王五", "id_card": "******************"},
            {"name": "李四", "id_card": "******************"}
        ]
        case_type_1 = "盗窃"

        start_time = datetime.now()
        result_1 = await SceneQzService.search_cases_with_shared_entities(
            entities=entities_1,
            case_type=case_type_1,
            page=1,
            page_size=10,
            db=db
        )
        end_time = datetime.now()
        execution_time_1 = (end_time - start_time).total_seconds()

        print(f"查询实体: {entities_1}")
        print(f"案件类型: {case_type_1}")
        print(f"执行时间: {execution_time_1:.3f} 秒")
        print(f"找到案件数量: {result_1.get('total', 0)}")

        if result_1.get('items'):
            print("找到的案件:")
            for i, item in enumerate(result_1['items']):
                print(f"  案件{i+1}: {item['case']}")
                print(f"    案件类型: {item['case_type']}")
                roles_info = [f"{r['name']}({r.get('id_card', '无身份证')})" for r in item['roles']]
                other_info = [f"{p['name']}({p.get('id_card', '无身份证')})" for p in item['other_involved']]
                print(f"    涉及角色: {roles_info}")
                print(f"    其他涉及人员: {other_info}")

        # 测试用例2：陈七和孙八（都没有身份证的共享实体）
        print("\n测试用例2：陈七和孙八（都没有身份证的共享实体）")
        print("-" * 60)

        entities_2 = [
            {"name": "陈七"},
            {"name": "孙八"}
        ]
        case_type_2 = "盗窃"

        start_time = datetime.now()
        result_2 = await SceneQzService.search_cases_with_shared_entities(
            entities=entities_2,
            case_type=case_type_2,
            page=1,
            page_size=10,
            db=db
        )
        end_time = datetime.now()
        execution_time_2 = (end_time - start_time).total_seconds()

        print(f"查询实体: {entities_2}")
        print(f"案件类型: {case_type_2}")
        print(f"执行时间: {execution_time_2:.3f} 秒")
        print(f"找到案件数量: {result_2.get('total', 0)}")

        if result_2.get('items'):
            print("找到的案件:")
            for i, item in enumerate(result_2['items']):
                print(f"  案件{i+1}: {item['case']}")
                roles_info = [f"{r['name']}({r.get('id_card', '无身份证')})" for r in item['roles']]
                print(f"    涉及角色: {roles_info}")

        # 测试用例3：王五和陈七（混合身份证情况）
        print("\n测试用例3：王五和陈七（混合身份证情况）")
        print("-" * 60)

        entities_3 = [
            {"name": "王五", "id_card": "******************"},
            {"name": "陈七"}  # 没有身份证
        ]
        case_type_3 = "抢劫"

        start_time = datetime.now()
        result_3 = await SceneQzService.search_cases_with_shared_entities(
            entities=entities_3,
            case_type=case_type_3,
            page=1,
            page_size=10,
            db=db
        )
        end_time = datetime.now()
        execution_time_3 = (end_time - start_time).total_seconds()

        print(f"查询实体: {entities_3}")
        print(f"案件类型: {case_type_3}")
        print(f"执行时间: {execution_time_3:.3f} 秒")
        print(f"找到案件数量: {result_3.get('total', 0)}")

        if result_3.get('items'):
            print("找到的案件:")
            for i, item in enumerate(result_3['items']):
                print(f"  案件{i+1}: {item['case']}")
                roles_info = [f"{r['name']}({r.get('id_card', '无身份证')})" for r in item['roles']]
                print(f"    涉及角色: {roles_info}")

        # 测试用例4：张三和李四（诈骗案）
        print("\n测试用例4：张三和李四（诈骗案）")
        print("-" * 60)

        entities_4 = [
            {"name": "张三", "id_card": "******************"},
            {"name": "李四", "id_card": "******************"}
        ]
        case_type_4 = "诈骗"

        start_time = datetime.now()
        result_4 = await SceneQzService.search_cases_with_shared_entities(
            entities=entities_4,
            case_type=case_type_4,
            page=1,
            page_size=10,
            db=db
        )
        end_time = datetime.now()
        execution_time_4 = (end_time - start_time).total_seconds()

        print(f"查询实体: {entities_4}")
        print(f"案件类型: {case_type_4}")
        print(f"执行时间: {execution_time_4:.3f} 秒")
        print(f"找到案件数量: {result_4.get('total', 0)}")

        if result_4.get('items'):
            print("找到的案件:")
            for i, item in enumerate(result_4['items']):
                print(f"  案件{i+1}: {item['case']}")
                roles_info = [f"{r['name']}({r.get('id_card', '无身份证')})" for r in item['roles']]
                print(f"    涉及角色: {roles_info}")

        # 测试用例5：大量实体性能测试
        print("\n测试用例5：大量实体性能测试（5个实体）")
        print("-" * 60)

        entities_5 = [
            {"name": "王五", "id_card": "******************"},
            {"name": "李四", "id_card": "******************"},
            {"name": "陈七"},
            {"name": "赵六", "id_card": "310101199504043456"},
            {"name": "孙八"}
        ]
        case_type_5 = "盗窃"

        start_time = datetime.now()
        result_5 = await SceneQzService.search_cases_with_shared_entities(
            entities=entities_5,
            case_type=case_type_5,
            page=1,
            page_size=10,
            db=db
        )
        end_time = datetime.now()
        execution_time_5 = (end_time - start_time).total_seconds()

        print(f"查询{len(entities_5)}个实体")
        print(f"案件类型: {case_type_5}")
        print(f"执行时间: {execution_time_5:.3f} 秒")
        print(f"找到案件数量: {result_5.get('total', 0)}")

        if result_5.get('items'):
            print("找到的案件:")
            for i, item in enumerate(result_5['items']):
                print(f"  案件{i+1}: {item['case']}")
                print(f"    涉及角色数量: {len(item['roles'])}")
                print(f"    其他涉及人员数量: {len(item['other_involved'])}")

        # 测试用例6：同名不同人测试
        print("\n测试用例6：同名不同人测试（两个张三）")
        print("-" * 60)

        entities_6 = [
            {"name": "张三", "id_card": "******************"},  # 第一个张三
            {"name": "张三", "id_card": "******************"}   # 第二个张三
        ]
        case_type_6 = "诈骗"

        start_time = datetime.now()
        result_6 = await SceneQzService.search_cases_with_shared_entities(
            entities=entities_6,
            case_type=case_type_6,
            page=1,
            page_size=10,
            db=db
        )
        end_time = datetime.now()
        execution_time_6 = (end_time - start_time).total_seconds()

        print(f"查询实体: {entities_6}")
        print(f"案件类型: {case_type_6}")
        print(f"执行时间: {execution_time_6:.3f} 秒")
        print(f"找到案件数量: {result_6.get('total', 0)}")

        if result_6.get('items'):
            print("找到的案件:")
            for i, item in enumerate(result_6['items']):
                print(f"  案件{i+1}: {item['case']}")
                roles_info = [f"{r['name']}({r.get('id_card', '无身份证')})" for r in item['roles']]
                print(f"    涉及角色: {roles_info}")

        # 测试用例7：分页测试
        print("\n测试用例7：分页测试")
        print("-" * 60)

        entities_7 = [
            {"name": "王五"},
            {"name": "李四"}
        ]
        case_type_7 = "盗窃"

        total_time = 0
        for page in range(1, 4):  # 测试前3页
            start_time = datetime.now()
            result_page = await SceneQzService.search_cases_with_shared_entities(
                entities=entities_7,
                case_type=case_type_7,
                page=page,
                page_size=2,  # 小页面大小以测试分页
                db=db
            )
            end_time = datetime.now()
            page_time = (end_time - start_time).total_seconds()
            total_time += page_time

            print(f"第{page}页执行时间: {page_time:.3f} 秒, 结果数量: {len(result_page.get('items', []))}")
            if result_page.get('items'):
                for item in result_page['items']:
                    print(f"    案件: {item['case']}")

        print(f"分页测试总时间: {total_time:.3f} 秒")
        print(f"平均每页时间: {total_time/3:.3f} 秒")

        # 测试用例8：边界情况测试
        print("\n测试用例8：边界情况测试")
        print("-" * 60)

        # 8.1 空实体列表
        start_time = datetime.now()
        result_8_1 = await SceneQzService.search_cases_with_shared_entities(
            entities=[],
            case_type="盗窃",
            page=1,
            page_size=10,
            db=db
        )
        end_time = datetime.now()
        execution_time_8_1 = (end_time - start_time).total_seconds()

        print(f"8.1 空实体列表 - 执行时间: {execution_time_8_1:.3f} 秒, 结果: {result_8_1.get('total', 0)}个案件")

        # 8.2 不存在的实体
        start_time = datetime.now()
        result_8_2 = await SceneQzService.search_cases_with_shared_entities(
            entities=[{"name": "不存在的人"}, {"name": "另一个不存在的人"}],
            case_type="盗窃",
            page=1,
            page_size=10,
            db=db
        )
        end_time = datetime.now()
        execution_time_8_2 = (end_time - start_time).total_seconds()

        print(f"8.2 不存在的实体 - 执行时间: {execution_time_8_2:.3f} 秒, 结果: {result_8_2.get('total', 0)}个案件")

        # 8.3 不存在的案件类型
        start_time = datetime.now()
        result_8_3 = await SceneQzService.search_cases_with_shared_entities(
            entities=[{"name": "王五"}, {"name": "李四"}],
            case_type="不存在的案件类型",
            page=1,
            page_size=10,
            db=db
        )
        end_time = datetime.now()
        execution_time_8_3 = (end_time - start_time).total_seconds()

        print(f"8.3 不存在的案件类型 - 执行时间: {execution_time_8_3:.3f} 秒, 结果: {result_8_3.get('total', 0)}个案件")

        # 性能总结
        print("\n" + "=" * 80)
        print("性能测试总结")
        print("=" * 80)
        print(f"测试用例1（都有身份证）: {execution_time_1:.3f} 秒")
        print(f"测试用例2（都没有身份证）: {execution_time_2:.3f} 秒")
        print(f"测试用例3（混合身份证情况）: {execution_time_3:.3f} 秒")
        print(f"测试用例4（诈骗案）: {execution_time_4:.3f} 秒")
        print(f"测试用例5（5个实体性能测试）: {execution_time_5:.3f} 秒")
        print(f"测试用例6（同名不同人）: {execution_time_6:.3f} 秒")
        print(f"分页测试平均时间: {total_time/3:.3f} 秒")
        print(f"边界测试平均时间: {(execution_time_8_1 + execution_time_8_2 + execution_time_8_3)/3:.3f} 秒")

        avg_time = (execution_time_1 + execution_time_2 + execution_time_3 +
                   execution_time_4 + execution_time_5 + execution_time_6 +
                   total_time/3 + (execution_time_8_1 + execution_time_8_2 + execution_time_8_3)/3) / 8
        print(f"总体平均执行时间: {avg_time:.3f} 秒")

        print("\n优化效果验证完成！")

    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        db.close()


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_optimized_function())
